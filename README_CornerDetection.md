# cbdetect::find_corners 优化方案

## 问题描述
原始的 `cbdetect::find_corners` 函数输出结果不够稳定，在相似的图像条件下可能产生不同数量和位置的角点。

## 优化方案

### 1. 稳定的角点检测函数 (`detectCornersStable`)

**功能**: 通过多次尝试不同参数组合来提高角点检测的稳定性

**主要改进**:
- 多次检测策略：使用3种不同的参数配置进行检测
- 自适应参数选择：根据图像特征动态调整检测参数
- 结果融合：选择检测到最多高质量角点的结果

**参数配置**:
```cpp
// 保守模式 (sensitivity = 0)
params.score_thr = 0.008;
params.detect_method = cbdetect::TemplateMatchSlow;

// 标准模式 (sensitivity = 1) 
params.score_thr = 0.003;
params.detect_method = cbdetect::LocalizedRadonTransform;

// 敏感模式 (sensitivity = 2)
params.score_thr = 0.001;
params.detect_method = cbdetect::HessianResponse;
```

### 2. 角点后处理优化 (`postProcessCorners`)

**功能**: 对检测到的角点进行过滤和优化

**主要改进**:
- 边界过滤：移除图像边缘附近的不稳定角点
- 非最大值抑制：移除过于接近的重复角点
- 质量排序：基于角点分数进行质量评估

### 3. 图像预处理优化 (`preprocessImageForCornerDetection`)

**功能**: 对输入图像进行预处理以提高检测效果

**处理类型**:
- **标准处理** (type=0): 轻微高斯模糊 + 对比度增强
- **增强对比度** (type=1): 直方图均衡化 + 强对比度增强  
- **降噪处理** (type=2): 双边滤波保持边缘

## 使用方法

### 基本使用
```cpp
#include "CornerDetectionUtils.h"

// 使用稳定检测函数
cbdetect::Corner corners = detectCornersStable(image, 3);

// 后处理优化
corners = postProcessCorners(corners, image);
```

### 高级使用
```cpp
// 自定义参数
cbdetect::Params params = getOptimizedParams(image, 1); // 标准敏感度

// 自定义预处理
cv::Mat processedImg = preprocessImageForCornerDetection(image, 0);

// 手动检测
cbdetect::find_corners(processedImg, corners, params);
```

## 关键参数说明

### 检测方法 (detect_method)
- `LocalizedRadonTransform`: 最稳定，适合大多数情况
- `HessianResponse`: 高敏感度，适合低对比度图像
- `TemplateMatchSlow`: 保守模式，适合噪声较多的图像

### 阈值参数
- `score_thr`: 角点质量阈值，越小检测越多角点
- `init_loc_thr`: 初始定位阈值，影响候选角点数量
- `norm_half_kernel_size`: 归一化核大小，影响局部对比度

### 多尺度检测
- `radius`: 检测半径数组，如 `{4, 6, 8}` 提供多尺度检测

## 性能对比

| 方法 | 角点数量稳定性 | 位置精度 | 计算时间 |
|------|---------------|----------|----------|
| 原始方法 | 低 | 中等 | 快 |
| 优化方法 | 高 | 高 | 中等 (3倍时间) |

## 建议使用场景

1. **高精度要求**: 使用 `detectCornersStable` + `postProcessCorners`
2. **实时性要求**: 使用 `getOptimizedParams` 获取单次最优参数
3. **特殊图像**: 根据图像特征选择合适的预处理类型

## 注意事项

1. 优化方法会增加约3倍的计算时间
2. 对于高质量图像，原始方法可能已经足够
3. 建议根据具体应用场景调整参数
4. 可以通过设置 `show_processing = true` 查看检测过程

## 编译说明

确保 CMakeLists.txt 包含新的源文件：
```cmake
set(SOURCES
    main.cpp
    CodCalc.cpp
    CornerDetectionUtils.cpp
    # ... 其他文件
)

set(HEADERS
    CodCalc.h
    CornerDetectionUtils.h
    # ... 其他头文件
)
```
